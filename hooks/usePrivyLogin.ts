import { useIdentityToken, usePrivy, useWallets } from "@privy-io/react-auth";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useCurrentAccount, useDisconnectWallet } from "@mysten/dapp-kit";
import { AppDispatch, RootState } from "@/store";
import { setUserAuth } from "@/store/user.store";
import Storage from "@/libs/storage";
import { LOGIN_METHODS, NETWORKS } from "@/utils/contants";
import { closeSocketInstance, createSocketInstance } from "@/libs/socket";
import rf from "@/services/RequestFactory";
import { useSessionSigners } from "@privy-io/react-auth";
import config from "@/config";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";

export const usePrivyLogin = () => {
  const { login, authenticated, user: privyUser, logout } = usePrivy();
  const { identityToken } = useIdentityToken();
  const { ready: walletsReady } = useWallets();
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const { mutate: disconnect } = useDisconnectWallet();
  const userId = useSelector((state: RootState) => state.user.userId);
  const { addSessionSigners, removeSessionSigners } = useSessionSigners();

  const exchangePrivyForJWT = useCallback(async (idToken: string) => {
    try {
      const response = await rf.getRequest("PrivyRequest").login(idToken);
      return response.jwtToken;
    } catch (error) {
      console.error("Failed to exchange Privy token for JWT:", error);
      throw error;
    }
  }, []);

  const handlePrivyAuthSuccess = useCallback(async () => {
    if (!identityToken) {
      console.error("No identity token available");
      return;
    }

    try {
      const jwtToken = await exchangePrivyForJWT(identityToken);

      dispatch(setUserAuth({ accessToken: jwtToken }));

      if (currentAccount?.address) {
        disconnect();
      }

      Storage.setLoginMethod(LOGIN_METHODS.PRIVY);

      const redirectAfterLogin = Storage.getRedirectAfterLogin();
      if (redirectAfterLogin) {
        const location = `${window.location.pathname}${window.location.search}`;
        if (location !== redirectAfterLogin) {
          Storage.clearRedirectAfterLogin();
          window.location.href = redirectAfterLogin;
        }
      }

      closeSocketInstance(NETWORKS.SUI);
      createSocketInstance(NETWORKS.SUI, jwtToken);

      console.log("Privy authentication successful");
    } catch (error) {
      console.error("Privy authentication failed:", error);
    }
  }, [
    identityToken,
    exchangePrivyForJWT,
    dispatch,
    currentAccount?.address,
    disconnect,
  ]);

  useEffect(() => {
    if (authenticated && identityToken && privyUser) {
      handlePrivyAuthSuccess();
    }
  }, [authenticated, identityToken, privyUser, handlePrivyAuthSuccess]);

  const onPrivyLogin = useCallback(() => {
    if (!authenticated) {
      login();
    }
  }, [login, authenticated]);

  const handlerRemoveSignerSession = useCallback(
    async (address: string) => {
      try {
        if (!walletsReady) {
          console.warn(
            "Wallet proxy not ready yet, skipping session signer removal"
          );
          return;
        }

        const res = await removeSessionSigners({
          address: address,
        });
        console.log(res, "remove session success");
      } catch (error) {
        console.error("Failed to remove Privy signer:", error);
      }
    },
    [walletsReady, removeSessionSigners]
  );

  const onPrivyLogout = useCallback(async () => {
    const currentLoginMethod = Storage.getLoginMethod();
    if (
      currentLoginMethod === LOGIN_METHODS.PRIVY &&
      privyUser?.wallet?.address
    ) {
      await handlerRemoveSignerSession(privyUser.wallet.address);
    }
    logout();
    Storage.clearLoginMethod();
  }, [logout, privyUser?.wallet?.address, handlerRemoveSignerSession]);

  const handlerAddSignerSession = useCallback(
    async (address: string) => {
      try {
        if (!walletsReady) {
          console.warn(
            "Wallet proxy not ready yet, skipping session signer setup"
          );
          return;
        }

        await addSessionSigners({
          signers: [
            {
              signerId: config.privyConfig.signerId,
            },
          ],
          address: address,
        });
      } catch (error) {
        console.error("Failed to add Privy signer:", error);
      }
    },
    [walletsReady, addSessionSigners]
  );

  const createPrivyWallet = useCallback(
    async (numberWallets: number) => {
      try {
        const response = await rf
          .getRequest("PrivyRequest")
          .createWallet(numberWallets);

        handlerAddSignerSession(response[0]);
        return response;
      } catch (error) {
        console.error("Failed to create Privy wallet:", error);
        throw error;
      }
    },
    [handlerAddSignerSession]
  );

  useEffect(() => {
    if (!walletsReady || !authenticated || !privyUser) {
      return;
    }

    if (privyUser?.wallet?.address) {
      handlerAddSignerSession(privyUser?.wallet.address);
      return;
    }

    createPrivyWallet(2);
  }, [
    privyUser?.wallet,
    userId,
    createPrivyWallet,
    walletsReady,
    authenticated,
    handlerAddSignerSession,
  ]);

  useEffect(() => {
    if (!authenticated || !privyUser?.wallet?.address) {
      return;
    }

    const handleBeforeUnload = async () => {
      const currentLoginMethod = Storage.getLoginMethod();
      if (
        currentLoginMethod === LOGIN_METHODS.PRIVY &&
        privyUser?.wallet?.address
      ) {
        await handlerRemoveSignerSession(privyUser.wallet.address);
      }
    };

    const handleVisibilityChange = async () => {
      // Only remove session signers if current login method is 'privy'
      const currentLoginMethod = Storage.getLoginMethod();
      if (
        document.visibilityState === "hidden" &&
        currentLoginMethod === LOGIN_METHODS.PRIVY &&
        privyUser?.wallet?.address
      ) {
        await handlerRemoveSignerSession(privyUser.wallet.address);
      }
    };

    // Add event listeners for page exit
    window.addEventListener("beforeunload", handleBeforeUnload);
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // Cleanup function to remove event listeners
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [authenticated, privyUser?.wallet?.address, handlerRemoveSignerSession]);

  useEffect(() => {
    AppBroadcast.on(BROADCAST_EVENTS.LOGOUT, onPrivyLogout);
    return () => {
      AppBroadcast.remove(BROADCAST_EVENTS.LOGOUT, onPrivyLogout);
    };
  }, [onPrivyLogout]);

  return {
    onPrivyLogin,
    onPrivyLogout,
    createPrivyWallet,
    authenticated,
    user: privyUser,
    identityToken,
  };
};
