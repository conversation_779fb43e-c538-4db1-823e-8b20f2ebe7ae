import React from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import {
  setIsShowModalLogin,
  setIsShowModalConnectTelegram,
} from "@/store/metadata.store";
import { Logo, Telegram, CloseIcon, LockIcon } from "@/assets/icons";
import ReactModal from "react-modal";
import { AppButton } from "../components/AppButton";
import { ConnectButton, useCurrentAccount } from "@mysten/dapp-kit";
import { usePrivyLogin } from "@/hooks";

export const ModalLogin = () => {
  const isOpen = useSelector(
    (state: RootState) => state.metadata.isShowModalLogin
  );
  const dispatch = useDispatch<AppDispatch>();
  const currentAccount = useCurrentAccount();
  const {
    onPrivyLogin,
    authenticated: privyAuthenticated,
    user: privyUser,
  } = usePrivyLogin();

  console.log({ privyAuthenticated, privyUser }, "privydata");

  const onClose = () => {
    dispatch(setIsShowModalLogin({ isShow: false }));
  };

  const onConnectTelegram = () => {
    dispatch(setIsShowModalConnectTelegram({ isShow: true }));
    dispatch(setIsShowModalLogin({ isShow: false }));
  };

  const customStyles = {
    content: {
      top: "50%",
      left: "50%",
      right: "auto",
      bottom: "auto",
      marginRight: "-50%",
      transform: "translate(-50%, -50%)",
      borderRadius: "16px",
      padding: 0,
      background: "#141518",
      border: "1px solid rgba(255, 255, 255, 0.05)",
      overflow: "inherit",
      boxShadow: "4px 4px 8px 0px rgba(8, 9, 12, 0.50)",
    },
    overlay: {
      background: "rgba(8, 9, 12, 0.70)",
      backdropFilter: "blur(7.5px)",
      zIndex: 999,
    },
  };

  return (
    <ReactModal
      isOpen={isOpen}
      style={customStyles}
      ariaHideApp={false}
      bodyOpenClassName="overflow-hidden"
      onRequestClose={onClose}
    >
      <div className="tablet:p-[56px] relative w-[calc(100vw-32px)] max-w-[888px] overflow-hidden rounded-[16px] p-4">
        <div
          className="text-white-500 hover:text-white-1000 absolute right-7 top-7 cursor-pointer"
          onClick={onClose}
        >
          <CloseIcon />
        </div>
        <div className="flex flex-col items-center">
          <div className="mb-6">
            <Logo />
          </div>

          <div className="text-brand-500 tablet:text-[40px] text-center text-[32px] font-semibold leading-[1.2]">
            Trade, snipe, copy trade on SUI at the speed of light
          </div>
          <div className="body-md-regular-14 mb-6 mt-4">
            Connect to start trading in less than 30 seconds
          </div>

          <AppButton
            onClick={onConnectTelegram}
            className="w-full max-w-[388px] items-center gap-2"
            variant="buy"
            size="large"
          >
            <Telegram />
            Connect Telegram
          </AppButton>

          <AppButton
            onClick={onPrivyLogin}
            className="mt-4 w-full max-w-[388px] items-center gap-2"
            variant="secondary"
            size="large"
          >
            <LockIcon className="h-[16px] w-[16px]" />
            Privy Login
          </AppButton>

          {!currentAccount?.address && (
            <ConnectButton
              className="!mt-10 !bg-transparent !p-0 !shadow-none"
              connectText={
                <div className="body-md-medium-14 !text-white-1000 flex cursor-pointer items-center gap-2">
                  Or Trading With Wallet{" "}
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M12.4697 5.46967C12.7626 5.17678 13.2374 5.17678 13.5303 5.46967L19.5303 11.4697C19.8232 11.7626 19.8232 12.2374 19.5303 12.5303L13.5303 18.5303C13.2374 18.8232 12.7626 18.8232 12.4697 18.5303C12.1768 18.2374 12.1768 17.7626 12.4697 17.4697L17.1893 12.75H5C4.58579 12.75 4.25 12.4142 4.25 12C4.25 11.5858 4.58579 11.25 5 11.25H17.1893L12.4697 6.53033C12.1768 6.23744 12.1768 5.76256 12.4697 5.46967Z"
                      fill="white"
                    />
                  </svg>
                </div>
              }
            />
          )}
        </div>
      </div>
    </ReactModal>
  );
};
